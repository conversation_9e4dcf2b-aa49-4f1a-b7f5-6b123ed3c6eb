Categories:
  - Science & Education
License: GPL-3.0-or-later
AuthorName: Ahmet Çetinkaya
AuthorEmail: <EMAIL>
WebSite: https://whph.ahmetcetinkaya.me
SourceCode: https://github.com/ahmet-cetinkaya/whph
IssueTracker: https://github.com/ahmet-cetinkaya/whph/issues
Donate: https://ahmetcetinkaya.me/donate

AutoName: Work Hard Play Hard

RepoType: git
Repo: https://github.com/ahmet-cetinkaya/whph

Builds:
  - versionName: 0.8.7
    versionCode: 36
    commit: v0.8.7
    output: build/app/outputs/flutter-apk/app-release.apk
    srclibs:
      - flutter@stable
    rm:
      - ios
      - linux
      - macos
      - web
      - windows
    prebuild:
      - git submodule update --init --recursive lib/corePackages/acore
      - flutterVersion=$(bash scripts/get_flutter_version.sh)
      - '[[$flutterVersion]] || exit 1'
      - git -C $$flutter$$ checkout -f $flutterVersion
      - export PUB_CACHE=$(pwd)/.pub-cache
      - $$flutter$$/bin/flutter config --no-analytics
      - $$flutter$$/bin/flutter packages pub get
    scandelete:
      - .pub-cache
    build:
      - export PUB_CACHE=$(pwd)/.pub-cache
      - $$flutter$$/bin/flutter build apk --release

AutoUpdateMode: Version
UpdateCheckMode: Tags
UpdateCheckData: pubspec.yaml|version:\s.+\+(\d+)|.|version:\s(.+)\+
CurrentVersion: 0.8.7+36
CurrentVersionCode: 36
