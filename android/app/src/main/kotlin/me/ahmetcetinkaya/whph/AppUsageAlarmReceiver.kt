package me.ahmetcetinkaya.whph

import android.content.BroadcastReceiver
import android.content.Context
import android.content.Intent
import android.app.AlarmManager
import android.app.PendingIntent
import android.os.Build
import java.util.*

class AppUsageAlarmReceiver : BroadcastReceiver() {
    
    override fun onReceive(context: Context, intent: Intent) {
        when (intent.action) {
            ACTION_COLLECT_APP_USAGE -> {
                // App usage verilerini topla
                collectAppUsageData(context)
                // Bir sonraki alarm'ı ayarla
                scheduleNextAlarm(context)
            }
        }
    }
    
    private fun collectAppUsageData(context: Context) {
        // Flutter method channel ile veri toplama işlemini tetikle
        // Bu kısım MainActivity'deki method channel ile iletişim kuracak
    }
    
    companion object {
        private const val ACTION_COLLECT_APP_USAGE = "me.ahmetcetinkaya.whph.COLLECT_APP_USAGE"
        private const val REQUEST_CODE = 1001
        
        fun schedulePeriodicCollection(context: Context, intervalMinutes: Long = 30) {
            val alarmManager = context.getSystemService(Context.ALARM_SERVICE) as AlarmManager
            val intent = Intent(context, AppUsageAlarmReceiver::class.java).apply {
                action = ACTION_COLLECT_APP_USAGE
            }
            
            val pendingIntent = PendingIntent.getBroadcast(
                context,
                REQUEST_CODE,
                intent,
                PendingIntent.FLAG_UPDATE_CURRENT or PendingIntent.FLAG_IMMUTABLE
            )
            
            val triggerTime = System.currentTimeMillis() + (intervalMinutes * 60 * 1000)
            
            if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.M) {
                alarmManager.setExactAndAllowWhileIdle(
                    AlarmManager.RTC_WAKEUP,
                    triggerTime,
                    pendingIntent
                )
            } else {
                alarmManager.setExact(
                    AlarmManager.RTC_WAKEUP,
                    triggerTime,
                    pendingIntent
                )
            }
        }
        
        private fun scheduleNextAlarm(context: Context) {
            schedulePeriodicCollection(context, 30) // 30 dakikada bir
        }
        
        fun cancelPeriodicCollection(context: Context) {
            val alarmManager = context.getSystemService(Context.ALARM_SERVICE) as AlarmManager
            val intent = Intent(context, AppUsageAlarmReceiver::class.java)
            val pendingIntent = PendingIntent.getBroadcast(
                context,
                REQUEST_CODE,
                intent,
                PendingIntent.FLAG_UPDATE_CURRENT or PendingIntent.FLAG_IMMUTABLE
            )
            alarmManager.cancel(pendingIntent)
        }
    }
}
