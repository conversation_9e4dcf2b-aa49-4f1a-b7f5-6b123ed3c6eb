package me.ahmetcetinkaya.whph

import android.content.Context
import androidx.work.*
import java.util.concurrent.TimeUnit
import io.flutter.plugin.common.MethodChannel
import io.flutter.embedding.engine.FlutterEngine
import io.flutter.embedding.engine.dart.DartExecutor

class AppUsageWorker(context: Context, params: WorkerParameters) : Worker(context, params) {
    
    override fun doWork(): Result {
        return try {
            // Flutter engine'i başlat ve app usage verilerini çek
            // Bu kısım Flutter method channel ile iletişim kuracak
            
            // Başarılı olursa
            Result.success()
        } catch (e: Exception) {
            // Hata durumunda retry
            Result.retry()
        }
    }
    
    companion object {
        private const val WORK_NAME = "app_usage_periodic_work"
        
        fun schedulePeriodicWork(context: Context) {
            val constraints = Constraints.Builder()
                .setRequiredNetworkType(NetworkType.NOT_REQUIRED)
                .setRequiresBatteryNotLow(false)
                .build()
                
            val periodicWorkRequest = PeriodicWorkRequestBuilder<AppUsageWorker>(
                15, TimeUnit.MINUTES // Minimum 15 dakika
            )
                .setConstraints(constraints)
                .build()
                
            WorkManager.getInstance(context).enqueueUniquePeriodicWork(
                WORK_NAME,
                ExistingPeriodicWorkPolicy.KEEP,
                periodicWorkRequest
            )
        }
        
        fun cancelPeriodicWork(context: Context) {
            WorkManager.getInstance(context).cancelUniqueWork(WORK_NAME)
        }
    }
}
