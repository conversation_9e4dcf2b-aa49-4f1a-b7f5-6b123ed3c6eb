package me.ahmetcetinkaya.whph

import android.content.Context
import android.content.SharedPreferences
import androidx.work.*
import java.util.concurrent.TimeUnit
import android.util.Log

class AppUsageWorker(context: Context, params: WorkerParameters) : Worker(context, params) {

    private val sharedPreferences: SharedPreferences =
        context.getSharedPreferences("app_usage_worker", Context.MODE_PRIVATE)

    override fun doWork(): Result {
        return try {
            Log.d("AppUsageWorker", "Starting app usage data collection...")

            // App usage verilerini toplamak için bir flag set et
            // MainActivity bu flag'i kontrol edecek ve veri toplama işlemini tetikleyecek
            sharedPreferences.edit()
                .putBoolean("should_collect_usage", true)
                .putLong("collection_timestamp", System.currentTimeMillis())
                .apply()

            Log.d("AppUsageWorker", "App usage collection flag set successfully")
            Result.success()
        } catch (e: Exception) {
            Log.e("AppUsageWorker", "Error in app usage worker", e)
            // Hata durumunda retry (maksimum 3 kez)
            if (runAttemptCount < 3) {
                Result.retry()
            } else {
                Result.failure()
            }
        }
    }

    companion object {
        private const val WORK_NAME = "app_usage_periodic_work"
        private const val TAG = "AppUsageWorker"

        fun schedulePeriodicWork(context: Context, intervalMinutes: Long = 30) {
            Log.d(TAG, "Scheduling periodic app usage work with interval: $intervalMinutes minutes")

            val constraints = Constraints.Builder()
                .setRequiredNetworkType(NetworkType.NOT_REQUIRED)
                .setRequiresBatteryNotLow(false)
                .setRequiresCharging(false)
                .setRequiresDeviceIdle(false)
                .build()

            val periodicWorkRequest = PeriodicWorkRequestBuilder<AppUsageWorker>(
                intervalMinutes, TimeUnit.MINUTES
            )
                .setConstraints(constraints)
                .setBackoffCriteria(
                    BackoffPolicy.LINEAR,
                    PeriodicWorkRequest.MIN_BACKOFF_MILLIS,
                    TimeUnit.MILLISECONDS
                )
                .build()

            WorkManager.getInstance(context).enqueueUniquePeriodicWork(
                WORK_NAME,
                ExistingPeriodicWorkPolicy.REPLACE,
                periodicWorkRequest
            )

            Log.d(TAG, "Periodic work scheduled successfully")
        }

        fun cancelPeriodicWork(context: Context) {
            Log.d(TAG, "Cancelling periodic app usage work")
            WorkManager.getInstance(context).cancelUniqueWork(WORK_NAME)
        }

        fun isWorkScheduled(context: Context): Boolean {
            val workInfos = WorkManager.getInstance(context)
                .getWorkInfosForUniqueWork(WORK_NAME)
                .get()

            return workInfos.any { workInfo ->
                workInfo.state == WorkInfo.State.ENQUEUED || workInfo.state == WorkInfo.State.RUNNING
            }
        }
    }
}
